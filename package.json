{"name": "huawei-heartrate-music-ai", "version": "1.0.0", "description": "Node.js application to collect heart rate data from Huawei wearables for AI music creation", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "collect": "node scripts/collectHeartRate.js", "analyze": "node scripts/analyzeData.js", "export-csv": "node scripts/exportToCsv.js", "test": "jest"}, "keywords": ["hua<PERSON>", "health-kit", "heart-rate", "ai-music", "music-creation", "wearable", "fitness", "api"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "node-cron": "^3.0.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}