# 华为手环心率数据收集器 - AI音乐创作版

这是一个Windows Forms应用程序，用于通过蓝牙连接华为手环，收集实时心率数据，并将数据保存为适合AI音乐创作的格式。

## 功能特性

### 🔗 蓝牙连接
- 扫描附近的华为手环设备
- 连接到指定的华为手环
- 实时监测连接状态
- 自动重连功能

### 📊 心率数据收集
- 实时显示当前心率
- 心率数据可视化进度条
- 历史数据列表显示
- 数据统计信息

### 💾 数据存储
- **JSON格式**: 完整的数据结构，包含所有字段
- **CSV格式**: 表格形式，便于Excel分析
- **AI音乐创作格式**: 专门为音乐创作优化的数据结构

### 🎵 AI音乐创作支持
每个心率数据点都包含以下音乐创作相关信息：
- **音乐节拍 (MusicTempo)**: 直接映射心率到BPM
- **强度 (Intensity)**: 0-1之间的强度值
- **节奏模式 (RhythmPattern)**: 4/4, 2/4, 1/4等
- **音乐调性 (MusicKey)**: C Major, G Major等建议调性
- **情绪状态 (EmotionalState)**: calm, relaxed, alert, excited等
- **BPM类别 (BpmCategory)**: resting, normal, elevated等

## 系统要求

- Windows 10/11
- .NET 6.0 或更高版本
- 蓝牙4.0+ (BLE支持)
- 华为手环 (支持心率监测的型号)

## 安装和运行

### 1. 编译项目
```bash
cd HuaweiHeartRateWinForms
dotnet build
```

### 2. 运行应用程序
```bash
dotnet run
```

或者直接运行编译后的exe文件。

## 使用说明

### 1. 连接设备
1. 确保华为手环已开启并且蓝牙可发现
2. 点击"扫描设备"按钮
3. 从设备列表中选择你的华为手环
4. 点击"连接"按钮

### 2. 开始监测
1. 连接成功后，点击"开始监测"按钮
2. 应用程序将开始实时显示心率数据
3. 数据会自动保存到内存缓存中

### 3. 保存数据
- **保存为JSON**: 完整的数据结构，包含元数据
- **保存为CSV**: 表格格式，便于数据分析
- **保存音乐数据**: 专门为AI音乐创作优化的格式

### 4. 数据文件位置
默认保存在: `我的文档\HeartRateData\`

## 数据格式说明

### JSON格式示例
```json
{
  "ExportTime": "2024-01-01T12:00:00",
  "DataCount": 100,
  "Data": [
    {
      "HeartRate": 75,
      "Timestamp": "2024-01-01T12:00:00",
      "BpmCategory": "normal",
      "Intensity": 0.25,
      "MusicTempo": 75,
      "RhythmPattern": "4/4",
      "MusicKey": "G Major",
      "EmotionalState": "relaxed"
    }
  ]
}
```

### AI音乐创作数据格式
```json
{
  "SessionInfo": {
    "Duration": 10.5,
    "DataPoints": 630,
    "AverageHeartRate": 78.5
  },
  "MusicParameters": {
    "TempoProgression": [...],
    "KeyProgression": [...],
    "RhythmPatterns": [...],
    "EmotionalJourney": [...]
  }
}
```

## AI音乐创作应用

### 节拍映射
- **静息 (50-60 BPM)**: 慢板音乐，冥想音乐
- **正常 (60-100 BPM)**: 流行音乐，轻音乐
- **轻度升高 (100-120 BPM)**: 舞曲，节奏感强的音乐
- **中等强度 (120-150 BPM)**: 快节奏舞曲，运动音乐
- **高强度 (150-180 BPM)**: 电子音乐，激烈的摇滚

### 情绪映射
- **calm**: 平静的音乐，小调，柔和的音色
- **relaxed**: 轻松的音乐，大调，温暖的音色
- **alert**: 明亮的音乐，清晰的节拍
- **excited**: 激昂的音乐，复杂的和声
- **intense**: 强烈的音乐，重低音，快节拍

### 调性建议
根据心率强度自动建议合适的音乐调性，从平静的C Major到激昂的B Major。

## 开发说明

### 项目结构
```
HuaweiHeartRateWinForms/
├── Models/
│   └── HeartRateData.cs          # 心率数据模型
├── Services/
│   ├── BluetoothHeartRateService.cs  # 蓝牙服务
│   └── DataStorageService.cs     # 数据存储服务
├── MainForm.cs                   # 主窗体
├── Program.cs                    # 程序入口
└── HuaweiHeartRateWinForms.csproj # 项目文件
```

### 扩展功能
- 可以添加更多的音乐理论映射
- 支持更多的数据导出格式
- 添加实时图表显示
- 集成音乐生成API

## 注意事项

1. **蓝牙权限**: 确保应用程序有蓝牙访问权限
2. **设备兼容性**: 目前支持标准BLE心率服务的华为设备
3. **数据隐私**: 所有数据都保存在本地，不会上传到云端
4. **电池消耗**: 长时间监测会消耗手环电池

## 故障排除

### 无法发现设备
- 确保手环蓝牙已开启
- 检查Windows蓝牙设置
- 重启蓝牙适配器

### 连接失败
- 确保手环未被其他设备连接
- 重启应用程序
- 重新配对设备

### 数据保存失败
- 检查磁盘空间
- 确保有写入权限
- 检查文件路径是否存在

## 许可证

MIT License - 详见LICENSE文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
