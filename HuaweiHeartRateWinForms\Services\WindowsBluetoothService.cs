using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Windows.Devices.Bluetooth;
using Windows.Devices.Bluetooth.Advertisement;
using Windows.Devices.Bluetooth.GenericAttributeProfile;
using Windows.Storage.Streams;
using HuaweiHeartRateWinForms.Models;

namespace HuaweiHeartRateWinForms.Services
{
    /// <summary>
    /// Windows蓝牙BLE服务 - 真实的蓝牙扫描和连接
    /// </summary>
    public class WindowsBluetoothService : IDisposable
    {
        private readonly BluetoothLEAdvertisementWatcher _watcher;
        private readonly Dictionary<ulong, BluetoothDevice> _discoveredDevices;
        private readonly System.Threading.Timer? _simulationTimer;
        private BluetoothLEDevice? _connectedDevice;
        private GattDeviceService? _heartRateService;
        private GattCharacteristic? _heartRateCharacteristic;
        
        private bool _isScanning = false;
        private bool _isConnected = false;
        private bool _isMonitoring = false;
        private int _simulatedHeartRate = 70;

        // 心率服务UUID
        private static readonly Guid HeartRateServiceUuid = new("0000180d-0000-1000-8000-00805f9b34fb");
        private static readonly Guid HeartRateMeasurementUuid = new("00002a37-0000-1000-8000-00805f9b34fb");

        // 华为设备名称模式
        private readonly string[] _huaweiDevicePatterns = {
            "HUAWEI WATCH",
            "HUAWEI Band",
            "HONOR Band",
            "GT 2",
            "GT 3",
            "Band 6",
            "Band 7",
            "Band 8",
            "Band 9"
        };

        // 事件
        public event EventHandler<BluetoothDevice>? DeviceFound;
        public event EventHandler<HeartRateData>? HeartRateReceived;
        public event EventHandler<string>? ConnectionStatusChanged;
        public event EventHandler<string>? ErrorOccurred;

        // 属性
        public bool IsConnected => _isConnected;
        public bool IsScanning => _isScanning;
        public bool IsMonitoring => _isMonitoring;
        public string? ConnectedDeviceAddress { get; private set; }
        public string? ConnectedDeviceName { get; private set; }

        public WindowsBluetoothService()
        {
            _discoveredDevices = new Dictionary<ulong, BluetoothDevice>();
            _watcher = new BluetoothLEAdvertisementWatcher
            {
                ScanningMode = BluetoothLEScanningMode.Active
            };

            _watcher.Received += OnAdvertisementReceived;
            _watcher.Stopped += OnWatcherStopped;

            // 初始化模拟定时器（用于心率数据生成）
            _simulationTimer = new System.Threading.Timer(GenerateSimulatedHeartRate, null, 
                System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
        }

        /// <summary>
        /// 开始扫描蓝牙设备
        /// </summary>
        public async Task StartScanAsync(int timeoutSeconds = 15)
        {
            if (_isScanning) return;

            try
            {
                _isScanning = true;
                _discoveredDevices.Clear();
                
                ConnectionStatusChanged?.Invoke(this, "开始扫描蓝牙BLE设备...");

                // 启动BLE广告监听
                _watcher.Start();

                // 设置扫描超时
                await Task.Delay(timeoutSeconds * 1000);
                
                if (_isScanning)
                {
                    StopScan();
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"扫描启动失败: {ex.Message}");
                _isScanning = false;
            }
        }

        /// <summary>
        /// 停止扫描
        /// </summary>
        public void StopScan()
        {
            if (!_isScanning) return;

            try
            {
                _watcher.Stop();
                _isScanning = false;
                ConnectionStatusChanged?.Invoke(this, $"扫描完成，发现 {_discoveredDevices.Count} 个设备");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"停止扫描失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接到指定设备
        /// </summary>
        public async Task<bool> ConnectAsync(string deviceAddress, string deviceName)
        {
            if (_isConnected) return true;

            try
            {
                ConnectionStatusChanged?.Invoke(this, $"正在连接到 {deviceName}...");

                // 查找设备
                var deviceInfo = _discoveredDevices.Values.FirstOrDefault(d => d.Address == deviceAddress);
                if (deviceInfo == null)
                {
                    ErrorOccurred?.Invoke(this, "未找到指定设备");
                    return false;
                }

                // 尝试连接到BLE设备
                var bluetoothAddress = Convert.ToUInt64(deviceAddress.Replace(":", ""), 16);
                _connectedDevice = await BluetoothLEDevice.FromBluetoothAddressAsync(bluetoothAddress);

                if (_connectedDevice == null)
                {
                    ErrorOccurred?.Invoke(this, "无法连接到设备");
                    return false;
                }

                // 获取心率服务
                var gattResult = await _connectedDevice.GetGattServicesForUuidAsync(HeartRateServiceUuid);
                if (gattResult.Status != GattCommunicationStatus.Success || !gattResult.Services.Any())
                {
                    // 如果没有找到心率服务，我们仍然可以连接并使用模拟数据
                    ConnectionStatusChanged?.Invoke(this, $"已连接到 {deviceName} (使用模拟心率数据)");
                }
                else
                {
                    _heartRateService = gattResult.Services.First();
                    
                    // 获取心率测量特征
                    var charResult = await _heartRateService.GetCharacteristicsForUuidAsync(HeartRateMeasurementUuid);
                    if (charResult.Status == GattCommunicationStatus.Success && charResult.Characteristics.Any())
                    {
                        _heartRateCharacteristic = charResult.Characteristics.First();
                        ConnectionStatusChanged?.Invoke(this, $"已连接到 {deviceName} (支持真实心率数据)");
                    }
                    else
                    {
                        ConnectionStatusChanged?.Invoke(this, $"已连接到 {deviceName} (使用模拟心率数据)");
                    }
                }

                _isConnected = true;
                ConnectedDeviceAddress = deviceAddress;
                ConnectedDeviceName = deviceName;

                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (!_isConnected) return;

            try
            {
                StopMonitoring();
                
                ConnectionStatusChanged?.Invoke(this, "正在断开连接...");

                _heartRateCharacteristic = null;
                _heartRateService?.Dispose();
                _heartRateService = null;
                _connectedDevice?.Dispose();
                _connectedDevice = null;

                _isConnected = false;
                ConnectedDeviceAddress = null;
                ConnectedDeviceName = null;

                await Task.Delay(500); // 给设备一些时间来处理断开连接

                ConnectionStatusChanged?.Invoke(this, "已断开连接");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"断开连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始心率监测
        /// </summary>
        public async void StartMonitoring()
        {
            if (!_isConnected || _isMonitoring) return;

            try
            {
                _isMonitoring = true;
                ConnectionStatusChanged?.Invoke(this, "开始心率监测...");

                if (_heartRateCharacteristic != null)
                {
                    // 尝试订阅真实的心率数据
                    var status = await _heartRateCharacteristic.WriteClientCharacteristicConfigurationDescriptorAsync(
                        GattClientCharacteristicConfigurationDescriptorValue.Notify);

                    if (status == GattCommunicationStatus.Success)
                    {
                        _heartRateCharacteristic.ValueChanged += OnHeartRateValueChanged;
                        ConnectionStatusChanged?.Invoke(this, "正在接收真实心率数据...");
                        return;
                    }
                }

                // 如果无法获取真实数据，使用模拟数据
                ConnectionStatusChanged?.Invoke(this, "使用模拟心率数据...");
                _simulationTimer?.Change(0, 1000); // 每秒生成一次模拟数据
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"开始监测失败: {ex.Message}");
                _isMonitoring = false;
            }
        }

        /// <summary>
        /// 停止心率监测
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            try
            {
                _isMonitoring = false;
                _simulationTimer?.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);

                if (_heartRateCharacteristic != null)
                {
                    _heartRateCharacteristic.ValueChanged -= OnHeartRateValueChanged;
                }

                ConnectionStatusChanged?.Invoke(this, "心率监测已停止");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"停止监测失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理BLE广告接收事件
        /// </summary>
        private void OnAdvertisementReceived(BluetoothLEAdvertisementWatcher sender, BluetoothLEAdvertisementReceivedEventArgs args)
        {
            try
            {
                var deviceName = args.Advertisement.LocalName;
                if (string.IsNullOrEmpty(deviceName))
                {
                    // 尝试从广告数据中获取设备名称
                    var manufacturerData = args.Advertisement.ManufacturerData.FirstOrDefault();
                    if (manufacturerData != null)
                    {
                        // 华为的制造商ID通常是0x027D
                        if (manufacturerData.CompanyId == 0x027D)
                        {
                            deviceName = "HUAWEI Device";
                        }
                    }
                }

                // 检查是否是华为设备
                if (!string.IsNullOrEmpty(deviceName) && IsHuaweiDevice(deviceName))
                {
                    var address = FormatBluetoothAddress(args.BluetoothAddress);
                    
                    if (!_discoveredDevices.ContainsKey(args.BluetoothAddress))
                    {
                        var device = new BluetoothDevice(deviceName, address, args.RawSignalStrengthInDBm);
                        _discoveredDevices[args.BluetoothAddress] = device;
                        
                        DeviceFound?.Invoke(this, device);
                    }
                }
            }
            catch (Exception ex)
            {
                // 忽略单个广告处理错误，继续扫描
                System.Diagnostics.Debug.WriteLine($"处理广告失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否是华为设备
        /// </summary>
        private bool IsHuaweiDevice(string deviceName)
        {
            return _huaweiDevicePatterns.Any(pattern => 
                deviceName.Contains(pattern, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 格式化蓝牙地址
        /// </summary>
        private string FormatBluetoothAddress(ulong address)
        {
            var bytes = BitConverter.GetBytes(address);
            Array.Reverse(bytes);
            return string.Join(":", bytes.Skip(2).Select(b => b.ToString("X2")));
        }

        /// <summary>
        /// 处理心率数据变化
        /// </summary>
        private void OnHeartRateValueChanged(GattCharacteristic sender, GattValueChangedEventArgs args)
        {
            try
            {
                var reader = DataReader.FromBuffer(args.CharacteristicValue);
                var flags = reader.ReadByte();
                
                int heartRate;
                if ((flags & 0x01) == 0)
                {
                    // 8位心率值
                    heartRate = reader.ReadByte();
                }
                else
                {
                    // 16位心率值
                    heartRate = reader.ReadUInt16();
                }

                var heartRateData = new HeartRateData(heartRate, DateTime.Now, 
                    ConnectedDeviceAddress, ConnectedDeviceName);
                
                HeartRateReceived?.Invoke(this, heartRateData);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"处理心率数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成模拟心率数据
        /// </summary>
        private void GenerateSimulatedHeartRate(object? state)
        {
            if (!_isMonitoring) return;

            try
            {
                // 生成模拟心率变化
                var random = new Random();
                var variation = (random.NextDouble() - 0.5) * 10; // ±5的变化
                _simulatedHeartRate = Math.Max(50, Math.Min(180, _simulatedHeartRate + (int)variation));

                var heartRateData = new HeartRateData(_simulatedHeartRate, DateTime.Now,
                    ConnectedDeviceAddress, ConnectedDeviceName);

                HeartRateReceived?.Invoke(this, heartRateData);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"生成模拟数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 监听器停止事件
        /// </summary>
        private void OnWatcherStopped(BluetoothLEAdvertisementWatcher sender, BluetoothLEAdvertisementWatcherStoppedEventArgs args)
        {
            _isScanning = false;
        }

        public void Dispose()
        {
            StopScan();
            _simulationTimer?.Dispose();
            _heartRateService?.Dispose();
            _connectedDevice?.Dispose();
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 蓝牙设备信息
    /// </summary>
    public class BluetoothDevice
    {
        public string Name { get; }
        public string Address { get; }
        public int Rssi { get; }

        public BluetoothDevice(string name, string address, int rssi)
        {
            Name = name;
            Address = address;
            Rssi = rssi;
        }

        public override string ToString()
        {
            return $"{Name} ({Address}) - 信号强度: {Rssi} dBm";
        }
    }
}
