using System;
using Newtonsoft.Json;

namespace HuaweiHeartRateWinForms.Models
{
    /// <summary>
    /// 心率数据模型
    /// </summary>
    public class HeartRateData
    {
        /// <summary>
        /// 心率值 (BPM)
        /// </summary>
        public int HeartRate { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 设备地址
        /// </summary>
        public string? DeviceAddress { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string? DeviceName { get; set; }

        /// <summary>
        /// BPM类别
        /// </summary>
        public string BpmCategory { get; set; } = string.Empty;

        /// <summary>
        /// 强度 (0-1)
        /// </summary>
        public double Intensity { get; set; }

        /// <summary>
        /// 音乐节拍
        /// </summary>
        public int MusicTempo { get; set; }

        /// <summary>
        /// 节奏模式
        /// </summary>
        public string RhythmPattern { get; set; } = string.Empty;

        /// <summary>
        /// 音乐调性建议
        /// </summary>
        public string MusicKey { get; set; } = string.Empty;

        /// <summary>
        /// 情绪状态
        /// </summary>
        public string EmotionalState { get; set; } = string.Empty;

        public HeartRateData()
        {
            Timestamp = DateTime.Now;
            CalculateMusicProperties();
        }

        public HeartRateData(int heartRate, DateTime timestamp, string? deviceAddress = null, string? deviceName = null)
        {
            HeartRate = heartRate;
            Timestamp = timestamp;
            DeviceAddress = deviceAddress;
            DeviceName = deviceName;
            CalculateMusicProperties();
        }

        /// <summary>
        /// 计算音乐相关属性
        /// </summary>
        private void CalculateMusicProperties()
        {
            BpmCategory = CategorizeBPM(HeartRate);
            Intensity = CalculateIntensity(HeartRate);
            MusicTempo = HeartRateToTempo(HeartRate);
            RhythmPattern = GenerateRhythmPattern(HeartRate);
            MusicKey = SuggestMusicKey(HeartRate);
            EmotionalState = DetermineEmotionalState(HeartRate);
        }

        /// <summary>
        /// 根据心率分类BPM类别
        /// </summary>
        private string CategorizeBPM(int heartRate)
        {
            return heartRate switch
            {
                < 60 => "resting",      // 静息
                < 100 => "normal",      // 正常
                < 120 => "elevated",    // 轻度升高
                < 150 => "moderate",    // 中等强度
                < 180 => "vigorous",    // 高强度
                _ => "maximum"          // 最大强度
            };
        }

        /// <summary>
        /// 计算心率强度
        /// </summary>
        private double CalculateIntensity(int heartRate)
        {
            const int maxHeartRate = 200;
            const int restingHeartRate = 60;
            
            var intensity = Math.Max(0.0, Math.Min(1.0, 
                (double)(heartRate - restingHeartRate) / (maxHeartRate - restingHeartRate)));
            
            return Math.Round(intensity, 2);
        }

        /// <summary>
        /// 将心率转换为音乐节拍
        /// </summary>
        private int HeartRateToTempo(int heartRate)
        {
            // 将心率映射到常见的音乐节拍范围 (60-180 BPM)
            return heartRate switch
            {
                < 60 => Math.Max(60, heartRate),
                > 180 => 180,
                _ => heartRate
            };
        }

        /// <summary>
        /// 根据心率生成节奏模式
        /// </summary>
        private string GenerateRhythmPattern(int heartRate)
        {
            return BpmCategory switch
            {
                "resting" => "4/4",     // 慢节拍
                "normal" => "4/4",      // 标准节拍
                "elevated" => "4/4",    // 稍快节拍
                "moderate" => "2/4",    // 快节拍
                "vigorous" => "2/4",    // 很快节拍
                "maximum" => "1/4",     // 极快节拍
                _ => "4/4"
            };
        }

        /// <summary>
        /// 根据心率建议音乐调性
        /// </summary>
        private string SuggestMusicKey(int heartRate)
        {
            return BpmCategory switch
            {
                "resting" => "C Major",     // 平静的大调
                "normal" => "G Major",      // 愉快的大调
                "elevated" => "D Major",    // 明亮的大调
                "moderate" => "A Major",    // 激昂的大调
                "vigorous" => "E Major",    // 强烈的大调
                "maximum" => "B Major",     // 极度激昂的大调
                _ => "C Major"
            };
        }

        /// <summary>
        /// 根据心率确定情绪状态
        /// </summary>
        private string DetermineEmotionalState(int heartRate)
        {
            return BpmCategory switch
            {
                "resting" => "calm",        // 平静
                "normal" => "relaxed",      // 放松
                "elevated" => "alert",      // 警觉
                "moderate" => "excited",    // 兴奋
                "vigorous" => "intense",    // 紧张
                "maximum" => "extreme",     // 极度
                _ => "neutral"
            };
        }

        /// <summary>
        /// 转换为CSV格式字符串
        /// </summary>
        public string ToCsvString()
        {
            return $"{Timestamp:yyyy-MM-dd HH:mm:ss},{HeartRate},{BpmCategory},{Intensity:F2},{MusicTempo},{RhythmPattern},{MusicKey},{EmotionalState},{DeviceAddress},{DeviceName}";
        }

        /// <summary>
        /// CSV标题行
        /// </summary>
        public static string CsvHeader => "Timestamp,HeartRate,BpmCategory,Intensity,MusicTempo,RhythmPattern,MusicKey,EmotionalState,DeviceAddress,DeviceName";

        /// <summary>
        /// 转换为JSON字符串
        /// </summary>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// 从JSON字符串创建对象
        /// </summary>
        public static HeartRateData? FromJson(string json)
        {
            try
            {
                return JsonConvert.DeserializeObject<HeartRateData>(json);
            }
            catch
            {
                return null;
            }
        }

        public override string ToString()
        {
            return $"{Timestamp:HH:mm:ss} - {HeartRate} BPM ({BpmCategory}) - Tempo: {MusicTempo} - Key: {MusicKey}";
        }
    }
}
