const axios = require('axios');
const crypto = require('crypto');

/**
 * 华为Health Kit API客户端
 * 用于与华为健康数据API进行交互
 */
class HuaweiHealthClient {
  constructor(config) {
    this.clientId = config.clientId;
    this.clientSecret = config.clientSecret;
    this.redirectUri = config.redirectUri;
    this.baseUrl = 'https://oauth-login.cloud.huawei.com';
    this.apiBaseUrl = 'https://health-api.cloud.huawei.com';
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
  }

  /**
   * 生成授权URL
   * @param {string} state - 状态参数，用于防止CSRF攻击
   * @returns {string} 授权URL
   */
  getAuthorizationUrl(state = null) {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      scope: 'https://www.huawei.com/healthkit/heartrate.read https://www.huawei.com/healthkit/heartrate.write',
      access_type: 'offline'
    });

    if (state) {
      params.append('state', state);
    }

    return `${this.baseUrl}/oauth2/v3/authorize?${params.toString()}`;
  }

  /**
   * 使用授权码获取访问令牌
   * @param {string} authorizationCode - 授权码
   * @returns {Promise<Object>} 令牌信息
   */
  async getAccessToken(authorizationCode) {
    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/v3/token`, {
        grant_type: 'authorization_code',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        code: authorizationCode,
        redirect_uri: this.redirectUri
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.refreshToken = tokenData.refresh_token;
      this.tokenExpiry = Date.now() + (tokenData.expires_in * 1000);

      return tokenData;
    } catch (error) {
      throw new Error(`获取访问令牌失败: ${error.response?.data?.error_description || error.message}`);
    }
  }

  /**
   * 刷新访问令牌
   * @returns {Promise<Object>} 新的令牌信息
   */
  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('没有可用的刷新令牌');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/v3/token`, {
        grant_type: 'refresh_token',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        refresh_token: this.refreshToken
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      if (tokenData.refresh_token) {
        this.refreshToken = tokenData.refresh_token;
      }
      this.tokenExpiry = Date.now() + (tokenData.expires_in * 1000);

      return tokenData;
    } catch (error) {
      throw new Error(`刷新访问令牌失败: ${error.response?.data?.error_description || error.message}`);
    }
  }

  /**
   * 检查并确保访问令牌有效
   * @returns {Promise<void>}
   */
  async ensureValidToken() {
    if (!this.accessToken) {
      throw new Error('没有访问令牌，请先进行授权');
    }

    // 如果令牌即将过期（提前5分钟刷新）
    if (this.tokenExpiry && Date.now() > (this.tokenExpiry - 300000)) {
      await this.refreshAccessToken();
    }
  }

  /**
   * 发送API请求
   * @param {string} endpoint - API端点
   * @param {string} method - HTTP方法
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} API响应
   */
  async makeApiRequest(endpoint, method = 'GET', data = null) {
    await this.ensureValidToken();

    const config = {
      method,
      url: `${this.apiBaseUrl}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      config.data = data;
    }

    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response?.status === 401) {
        // 令牌无效，尝试刷新
        await this.refreshAccessToken();
        config.headers['Authorization'] = `Bearer ${this.accessToken}`;
        const retryResponse = await axios(config);
        return retryResponse.data;
      }
      throw new Error(`API请求失败: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * 设置访问令牌（用于从存储中恢复）
   * @param {string} accessToken - 访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @param {number} expiryTime - 过期时间戳
   */
  setTokens(accessToken, refreshToken, expiryTime) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.tokenExpiry = expiryTime;
  }

  /**
   * 获取当前令牌信息
   * @returns {Object} 令牌信息
   */
  getTokenInfo() {
    return {
      accessToken: this.accessToken,
      refreshToken: this.refreshToken,
      tokenExpiry: this.tokenExpiry,
      isValid: this.accessToken && (!this.tokenExpiry || Date.now() < this.tokenExpiry)
    };
  }
}

module.exports = HuaweiHealthClient;
