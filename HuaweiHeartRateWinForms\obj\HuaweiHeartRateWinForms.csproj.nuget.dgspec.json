{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\aimusic\\HuaweiHeartRateWinForms\\HuaweiHeartRateWinForms.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\aimusic\\HuaweiHeartRateWinForms\\HuaweiHeartRateWinForms.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\aimusic\\HuaweiHeartRateWinForms\\HuaweiHeartRateWinForms.csproj", "projectName": "HuaweiHeartRateWinForms", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\aimusic\\HuaweiHeartRateWinForms\\HuaweiHeartRateWinForms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\aimusic\\HuaweiHeartRateWinForms\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "Microsoft.Windows.SDK.Contracts": {"target": "Package", "version": "[10.0.22621.2428, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OxyPlot.WindowsForms": {"target": "Package", "version": "[2.1.2, )"}, "System.IO.Ports": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}