const EventEmitter = require('events');
const moment = require('moment');

/**
 * 蓝牙心率数据收集器
 * 用于通过蓝牙BLE连接华为手环，获取实时心率数据
 */
class BluetoothHeartRateCollector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.deviceName = options.deviceName || 'HUAWEI WATCH';
    this.deviceAddress = options.deviceAddress || null;
    this.isConnected = false;
    this.isScanning = false;
    this.heartRateData = [];
    this.currentHeartRate = null;
    
    // 心率服务和特征UUID (标准BLE心率服务)
    this.HEART_RATE_SERVICE_UUID = '180d';
    this.HEART_RATE_MEASUREMENT_UUID = '2a37';
    this.BATTERY_SERVICE_UUID = '180f';
    this.BATTERY_LEVEL_UUID = '2a19';
    
    // 模拟数据选项
    this.simulateData = options.simulateData || false;
    this.simulationInterval = null;
    
    this.init();
  }

  /**
   * 初始化蓝牙收集器
   */
  async init() {
    try {
      // 在Windows环境下，我们提供一个模拟实现
      // 实际部署时可以替换为真实的蓝牙库
      console.log('蓝牙心率收集器初始化完成');
      this.emit('ready');
    } catch (error) {
      console.error('蓝牙初始化失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 开始扫描蓝牙设备
   * @param {number} timeout - 扫描超时时间（毫秒）
   */
  async startScan(timeout = 10000) {
    if (this.isScanning) {
      console.log('已在扫描中...');
      return;
    }

    console.log('开始扫描蓝牙设备...');
    this.isScanning = true;
    this.emit('scanStart');

    // 模拟扫描过程
    setTimeout(() => {
      const mockDevices = [
        {
          name: 'HUAWEI WATCH GT 3',
          address: 'AA:BB:CC:DD:EE:FF',
          rssi: -45,
          services: [this.HEART_RATE_SERVICE_UUID]
        },
        {
          name: 'HUAWEI Band 7',
          address: 'FF:EE:DD:CC:BB:AA',
          rssi: -52,
          services: [this.HEART_RATE_SERVICE_UUID]
        }
      ];

      mockDevices.forEach(device => {
        this.emit('deviceFound', device);
      });

      this.isScanning = false;
      this.emit('scanComplete', mockDevices);
    }, 3000);

    // 设置扫描超时
    setTimeout(() => {
      if (this.isScanning) {
        this.stopScan();
      }
    }, timeout);
  }

  /**
   * 停止扫描
   */
  stopScan() {
    if (!this.isScanning) return;
    
    console.log('停止扫描');
    this.isScanning = false;
    this.emit('scanStop');
  }

  /**
   * 连接到指定设备
   * @param {string} deviceAddress - 设备MAC地址
   */
  async connect(deviceAddress) {
    if (this.isConnected) {
      console.log('已连接到设备');
      return;
    }

    try {
      console.log(`正在连接到设备: ${deviceAddress}`);
      this.deviceAddress = deviceAddress;
      
      // 模拟连接过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.isConnected = true;
      console.log('设备连接成功');
      this.emit('connected', { address: deviceAddress });
      
      // 开始监听心率数据
      this.startHeartRateMonitoring();
      
    } catch (error) {
      console.error('连接失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 断开连接
   */
  async disconnect() {
    if (!this.isConnected) return;

    try {
      console.log('正在断开连接...');
      
      this.stopHeartRateMonitoring();
      this.isConnected = false;
      this.deviceAddress = null;
      
      console.log('设备已断开连接');
      this.emit('disconnected');
      
    } catch (error) {
      console.error('断开连接失败:', error);
      this.emit('error', error);
    }
  }

  /**
   * 开始心率监测
   */
  startHeartRateMonitoring() {
    if (!this.isConnected) {
      throw new Error('设备未连接');
    }

    console.log('开始心率监测...');
    
    // 如果启用模拟数据，生成模拟心率数据
    if (this.simulateData) {
      this.startSimulation();
    } else {
      // 这里应该是真实的蓝牙心率数据监听
      console.log('等待真实心率数据...');
    }
    
    this.emit('monitoringStart');
  }

  /**
   * 停止心率监测
   */
  stopHeartRateMonitoring() {
    console.log('停止心率监测');
    
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
    }
    
    this.emit('monitoringStop');
  }

  /**
   * 开始模拟心率数据
   */
  startSimulation() {
    let baseHeartRate = 70; // 基础心率
    let trend = 0; // 趋势：-1下降，0稳定，1上升
    let trendCounter = 0;
    
    this.simulationInterval = setInterval(() => {
      // 模拟心率变化
      if (trendCounter <= 0) {
        trend = Math.random() > 0.5 ? 1 : -1;
        trendCounter = Math.floor(Math.random() * 10) + 5; // 5-15次测量保持同一趋势
      }
      
      // 生成心率变化
      const variation = (Math.random() - 0.5) * 10; // ±5的随机变化
      const trendEffect = trend * Math.random() * 3; // 趋势影响
      
      baseHeartRate += trendEffect + variation;
      baseHeartRate = Math.max(50, Math.min(180, baseHeartRate)); // 限制在合理范围内
      
      const heartRate = Math.round(baseHeartRate);
      trendCounter--;
      
      this.processHeartRateData(heartRate);
    }, 1000); // 每秒一次心率数据
  }

  /**
   * 处理接收到的心率数据
   * @param {number} heartRate - 心率值
   */
  processHeartRateData(heartRate) {
    const timestamp = Date.now();
    const heartRateData = {
      heartRate: heartRate,
      timestamp: timestamp,
      dateTime: moment(timestamp).format('YYYY-MM-DD HH:mm:ss'),
      date: moment(timestamp).format('YYYY-MM-DD'),
      time: moment(timestamp).format('HH:mm:ss'),
      deviceAddress: this.deviceAddress,
      // AI音乐创作相关字段
      bpmCategory: this.categorizeBPM(heartRate),
      intensity: this.calculateIntensity(heartRate),
      musicTempo: this.heartRateToTempo(heartRate),
      rhythmPattern: this.generateRhythmPattern(heartRate)
    };

    this.currentHeartRate = heartRateData;
    this.heartRateData.push(heartRateData);
    
    // 限制内存中保存的数据量
    if (this.heartRateData.length > 1000) {
      this.heartRateData = this.heartRateData.slice(-1000);
    }

    console.log(`心率: ${heartRate} BPM`);
    this.emit('heartRateData', heartRateData);
  }

  /**
   * 根据心率分类BPM类别
   */
  categorizeBPM(heartRate) {
    if (heartRate < 60) return 'resting';
    if (heartRate < 100) return 'normal';
    if (heartRate < 120) return 'elevated';
    if (heartRate < 150) return 'moderate';
    if (heartRate < 180) return 'vigorous';
    return 'maximum';
  }

  /**
   * 计算心率强度
   */
  calculateIntensity(heartRate) {
    const maxHeartRate = 200;
    const restingHeartRate = 60;
    const intensity = Math.max(0, Math.min(1, 
      (heartRate - restingHeartRate) / (maxHeartRate - restingHeartRate)
    ));
    return Math.round(intensity * 100) / 100;
  }

  /**
   * 将心率转换为音乐节拍
   */
  heartRateToTempo(heartRate) {
    // 将心率映射到音乐节拍范围
    if (heartRate < 60) return Math.max(60, heartRate);
    if (heartRate > 180) return 180;
    return heartRate;
  }

  /**
   * 根据心率生成节奏模式
   */
  generateRhythmPattern(heartRate) {
    const patterns = {
      resting: '4/4',     // 慢节拍
      normal: '4/4',      // 标准节拍
      elevated: '4/4',    // 稍快节拍
      moderate: '2/4',    // 快节拍
      vigorous: '2/4',    // 很快节拍
      maximum: '1/4'      // 极快节拍
    };
    
    const category = this.categorizeBPM(heartRate);
    return patterns[category] || '4/4';
  }

  /**
   * 获取当前心率
   */
  getCurrentHeartRate() {
    return this.currentHeartRate;
  }

  /**
   * 获取历史心率数据
   */
  getHeartRateHistory(limit = 100) {
    return this.heartRateData.slice(-limit);
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      isScanning: this.isScanning,
      deviceAddress: this.deviceAddress,
      dataCount: this.heartRateData.length
    };
  }

  /**
   * 清除历史数据
   */
  clearHistory() {
    this.heartRateData = [];
    console.log('历史数据已清除');
  }
}

module.exports = BluetoothHeartRateCollector;
