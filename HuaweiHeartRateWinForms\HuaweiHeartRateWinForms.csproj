<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>华为手环心率数据收集器</AssemblyTitle>
    <AssemblyDescription>用于收集华为手环心率数据并保存为本地文件，供AI音乐创作使用</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="OxyPlot.WindowsForms" Version="2.1.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Data\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
  </ItemGroup>

</Project>
