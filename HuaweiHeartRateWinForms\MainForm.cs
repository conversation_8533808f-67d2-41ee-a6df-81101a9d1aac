using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HuaweiHeartRateWinForms.Models;
using HuaweiHeartRateWinForms.Services;

namespace HuaweiHeartRateWinForms
{
    public partial class MainForm : Form
    {
        private readonly BluetoothHeartRateService _bluetoothService;
        private readonly DataStorageService _dataStorage;
        
        // UI控件
        private Button btnScan = null!;
        private Button btnConnect = null!;
        private Button btnDisconnect = null!;
        private Button btnStartMonitoring = null!;
        private Button btnStopMonitoring = null!;
        private Button btnSaveJson = null!;
        private Button btnSaveCsv = null!;
        private Button btnSaveMusicData = null!;
        private Button btnClearData = null!;
        private ListBox lstDevices = null!;
        private ListBox lstHeartRateData = null!;
        private Label lblCurrentHeartRate = null!;
        private Label lblStatus = null!;
        private Label lblDataCount = null!;
        private Label lblStatistics = null!;
        private ProgressBar progressHeartRate = null!;
        private System.Windows.Forms.Timer uiUpdateTimer = null!;

        public MainForm()
        {
            _bluetoothService = new BluetoothHeartRateService();
            _dataStorage = new DataStorageService();
            
            InitializeComponent();
            SetupEventHandlers();
            SetupUIUpdateTimer();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            Text = "华为手环心率数据收集器 - AI音乐创作";
            Size = new Size(1000, 700);
            StartPosition = FormStartPosition.CenterScreen;
            MinimumSize = new Size(800, 600);

            // 创建主面板
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // 设置列和行的大小
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 200F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 70F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));

            // 左上：设备控制面板
            var devicePanel = CreateDevicePanel();
            mainPanel.Controls.Add(devicePanel, 0, 0);

            // 右上：心率显示面板
            var heartRatePanel = CreateHeartRatePanel();
            mainPanel.Controls.Add(heartRatePanel, 1, 0);

            // 左下：设备列表
            var deviceListPanel = CreateDeviceListPanel();
            mainPanel.Controls.Add(deviceListPanel, 0, 1);

            // 右下：数据列表
            var dataListPanel = CreateDataListPanel();
            mainPanel.Controls.Add(dataListPanel, 1, 1);

            // 底部：数据操作面板
            var dataOperationPanel = CreateDataOperationPanel();
            mainPanel.Controls.Add(dataOperationPanel, 0, 2);
            mainPanel.SetColumnSpan(dataOperationPanel, 2);

            Controls.Add(mainPanel);
        }

        private Panel CreateDevicePanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var layout = new TableLayoutPanel 
            { 
                Dock = DockStyle.Fill, 
                ColumnCount = 2, 
                RowCount = 3,
                Padding = new Padding(5)
            };

            btnScan = new Button { Text = "扫描设备", Dock = DockStyle.Fill };
            btnConnect = new Button { Text = "连接", Dock = DockStyle.Fill, Enabled = false };
            btnDisconnect = new Button { Text = "断开", Dock = DockStyle.Fill, Enabled = false };
            btnStartMonitoring = new Button { Text = "开始监测", Dock = DockStyle.Fill, Enabled = false };
            btnStopMonitoring = new Button { Text = "停止监测", Dock = DockStyle.Fill, Enabled = false };
            lblStatus = new Label { Text = "未连接", Dock = DockStyle.Fill, TextAlign = ContentAlignment.MiddleCenter };

            layout.Controls.Add(btnScan, 0, 0);
            layout.Controls.Add(btnConnect, 1, 0);
            layout.Controls.Add(btnDisconnect, 0, 1);
            layout.Controls.Add(btnStartMonitoring, 1, 1);
            layout.Controls.Add(btnStopMonitoring, 0, 2);
            layout.Controls.Add(lblStatus, 1, 2);

            panel.Controls.Add(layout);
            return panel;
        }

        private Panel CreateHeartRatePanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var layout = new TableLayoutPanel 
            { 
                Dock = DockStyle.Fill, 
                ColumnCount = 1, 
                RowCount = 4,
                Padding = new Padding(5)
            };

            lblCurrentHeartRate = new Label 
            { 
                Text = "-- BPM", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 24, FontStyle.Bold),
                ForeColor = Color.Red
            };

            progressHeartRate = new ProgressBar 
            { 
                Dock = DockStyle.Fill, 
                Minimum = 50, 
                Maximum = 180, 
                Value = 70 
            };

            lblDataCount = new Label 
            { 
                Text = "数据点: 0", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter 
            };

            lblStatistics = new Label 
            { 
                Text = "统计信息", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.TopLeft 
            };

            layout.Controls.Add(lblCurrentHeartRate, 0, 0);
            layout.Controls.Add(progressHeartRate, 0, 1);
            layout.Controls.Add(lblDataCount, 0, 2);
            layout.Controls.Add(lblStatistics, 0, 3);

            panel.Controls.Add(layout);
            return panel;
        }

        private Panel CreateDeviceListPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var groupBox = new GroupBox { Text = "发现的设备", Dock = DockStyle.Fill };
            
            lstDevices = new ListBox { Dock = DockStyle.Fill };
            groupBox.Controls.Add(lstDevices);
            panel.Controls.Add(groupBox);
            
            return panel;
        }

        private Panel CreateDataListPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var groupBox = new GroupBox { Text = "心率数据", Dock = DockStyle.Fill };
            
            lstHeartRateData = new ListBox { Dock = DockStyle.Fill };
            groupBox.Controls.Add(lstHeartRateData);
            panel.Controls.Add(groupBox);
            
            return panel;
        }

        private Panel CreateDataOperationPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var layout = new TableLayoutPanel 
            { 
                Dock = DockStyle.Fill, 
                ColumnCount = 4, 
                RowCount = 2,
                Padding = new Padding(5)
            };

            btnSaveJson = new Button { Text = "保存为JSON", Dock = DockStyle.Fill };
            btnSaveCsv = new Button { Text = "保存为CSV", Dock = DockStyle.Fill };
            btnSaveMusicData = new Button { Text = "保存音乐数据", Dock = DockStyle.Fill };
            btnClearData = new Button { Text = "清除数据", Dock = DockStyle.Fill };

            var lblDataDir = new Label 
            { 
                Text = $"数据目录: {_dataStorage.GetDataDirectory()}", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleLeft 
            };

            layout.Controls.Add(btnSaveJson, 0, 0);
            layout.Controls.Add(btnSaveCsv, 1, 0);
            layout.Controls.Add(btnSaveMusicData, 2, 0);
            layout.Controls.Add(btnClearData, 3, 0);
            layout.Controls.Add(lblDataDir, 0, 1);
            layout.SetColumnSpan(lblDataDir, 4);

            panel.Controls.Add(layout);
            return panel;
        }

        private void SetupEventHandlers()
        {
            // 蓝牙服务事件
            _bluetoothService.DeviceFound += OnDeviceFound;
            _bluetoothService.HeartRateReceived += OnHeartRateReceived;
            _bluetoothService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _bluetoothService.ErrorOccurred += OnErrorOccurred;

            // 按钮事件
            btnScan.Click += async (s, e) => await _bluetoothService.StartScanAsync();
            btnConnect.Click += OnConnectClick;
            btnDisconnect.Click += async (s, e) => await _bluetoothService.DisconnectAsync();
            btnStartMonitoring.Click += (s, e) => _bluetoothService.StartMonitoring();
            btnStopMonitoring.Click += (s, e) => _bluetoothService.StopMonitoring();
            
            btnSaveJson.Click += async (s, e) => await SaveData("json");
            btnSaveCsv.Click += async (s, e) => await SaveData("csv");
            btnSaveMusicData.Click += async (s, e) => await SaveData("music");
            btnClearData.Click += OnClearDataClick;

            // 列表选择事件
            lstDevices.SelectedIndexChanged += OnDeviceSelectionChanged;
        }

        private void SetupUIUpdateTimer()
        {
            uiUpdateTimer = new System.Windows.Forms.Timer { Interval = 1000 };
            uiUpdateTimer.Tick += UpdateUI;
            uiUpdateTimer.Start();
        }

        private void OnDeviceFound(object? sender, BluetoothDevice device)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDeviceFound(sender, device)));
                return;
            }

            lstDevices.Items.Add(device);
        }

        private void OnHeartRateReceived(object? sender, HeartRateData data)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnHeartRateReceived(sender, data)));
                return;
            }

            _dataStorage.AddToCache(data);
            
            // 更新心率显示
            lblCurrentHeartRate.Text = $"{data.HeartRate} BPM";
            progressHeartRate.Value = Math.Max(progressHeartRate.Minimum, 
                Math.Min(progressHeartRate.Maximum, data.HeartRate));

            // 添加到列表（只显示最新的50条）
            lstHeartRateData.Items.Insert(0, data.ToString());
            if (lstHeartRateData.Items.Count > 50)
            {
                lstHeartRateData.Items.RemoveAt(lstHeartRateData.Items.Count - 1);
            }
        }

        private void OnConnectionStatusChanged(object? sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, status)));
                return;
            }

            lblStatus.Text = status;
            UpdateButtonStates();
        }

        private void OnErrorOccurred(object? sender, string error)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnErrorOccurred(sender, error)));
                return;
            }

            MessageBox.Show(error, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private async void OnConnectClick(object? sender, EventArgs e)
        {
            if (lstDevices.SelectedItem is BluetoothDevice device)
            {
                await _bluetoothService.ConnectAsync(device.Address, device.Name);
            }
            else
            {
                MessageBox.Show("请先选择一个设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void OnDeviceSelectionChanged(object? sender, EventArgs e)
        {
            btnConnect.Enabled = lstDevices.SelectedItem != null && !_bluetoothService.IsConnected;
        }

        private void OnClearDataClick(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要清除所有数据吗？", "确认", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _dataStorage.ClearCache();
                lstHeartRateData.Items.Clear();
                lblCurrentHeartRate.Text = "-- BPM";
                progressHeartRate.Value = 70;
            }
        }

        private async System.Threading.Tasks.Task SaveData(string format)
        {
            try
            {
                switch (format)
                {
                    case "json":
                        await _dataStorage.SaveToJsonAsync();
                        break;
                    case "csv":
                        await _dataStorage.SaveToCsvAsync();
                        break;
                    case "music":
                        await _dataStorage.SaveForMusicCreationAsync();
                        break;
                }
                
                MessageBox.Show($"数据已保存到: {_dataStorage.GetDataDirectory()}", "保存成功", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateButtonStates()
        {
            btnConnect.Enabled = lstDevices.SelectedItem != null && !_bluetoothService.IsConnected;
            btnDisconnect.Enabled = _bluetoothService.IsConnected;
            btnStartMonitoring.Enabled = _bluetoothService.IsConnected && !_bluetoothService.IsMonitoring;
            btnStopMonitoring.Enabled = _bluetoothService.IsMonitoring;
        }

        private void UpdateUI(object? sender, EventArgs e)
        {
            // 更新数据计数
            var cachedData = _dataStorage.GetCachedData();
            lblDataCount.Text = $"数据点: {cachedData.Count}";

            // 更新统计信息
            if (cachedData.Count > 0)
            {
                var stats = _dataStorage.GetStatistics();
                lblStatistics.Text = $"平均心率: {stats.AverageHeartRate:F1} BPM\n" +
                                   $"范围: {stats.MinHeartRate}-{stats.MaxHeartRate} BPM\n" +
                                   $"时长: {stats.Duration:F1} 分钟\n" +
                                   $"主要状态: {stats.MostCommonEmotionalState}";
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _bluetoothService?.Dispose();
            uiUpdateTimer?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
