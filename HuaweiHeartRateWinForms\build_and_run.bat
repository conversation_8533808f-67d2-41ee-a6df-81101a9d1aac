@echo off
echo ========================================
echo 华为手环心率数据收集器 - 编译和运行
echo ========================================

echo.
echo 正在检查.NET环境...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到.NET SDK
    echo 请先安装.NET 6.0或更高版本
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET环境检查通过
echo.

echo 正在清理旧的编译文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo.
echo 正在还原NuGet包...
dotnet restore
if errorlevel 1 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
dotnet build --configuration Release
if errorlevel 1 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo.

echo 正在启动应用程序...
echo.
dotnet run --configuration Release

echo.
echo 应用程序已退出
pause
