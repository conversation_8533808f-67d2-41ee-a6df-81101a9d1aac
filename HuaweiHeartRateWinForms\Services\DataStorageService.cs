using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CsvHelper;
using CsvHelper.Configuration;
using Newtonsoft.Json;
using HuaweiHeartRateWinForms.Models;
using System.Globalization;

namespace HuaweiHeartRateWinForms.Services
{
    /// <summary>
    /// 数据存储服务
    /// </summary>
    public class DataStorageService
    {
        private readonly string _dataDirectory;
        private readonly List<HeartRateData> _memoryCache;

        public DataStorageService(string? dataDirectory = null)
        {
            _dataDirectory = dataDirectory ?? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "HeartRateData");
            _memoryCache = new List<HeartRateData>();
            
            // 确保数据目录存在
            Directory.CreateDirectory(_dataDirectory);
        }

        /// <summary>
        /// 保存心率数据到内存缓存
        /// </summary>
        public void AddToCache(HeartRateData data)
        {
            _memoryCache.Add(data);
            
            // 限制内存缓存大小
            if (_memoryCache.Count > 10000)
            {
                _memoryCache.RemoveRange(0, 1000); // 移除最旧的1000条记录
            }
        }

        /// <summary>
        /// 获取内存缓存中的数据
        /// </summary>
        public List<HeartRateData> GetCachedData(int? limit = null)
        {
            if (limit.HasValue && limit.Value > 0)
            {
                return _memoryCache.TakeLast(limit.Value).ToList();
            }
            return new List<HeartRateData>(_memoryCache);
        }

        /// <summary>
        /// 清除内存缓存
        /// </summary>
        public void ClearCache()
        {
            _memoryCache.Clear();
        }

        /// <summary>
        /// 保存数据到JSON文件
        /// </summary>
        public async Task SaveToJsonAsync(string? fileName = null)
        {
            fileName ??= $"heartrate_data_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(_dataDirectory, fileName);

            var jsonData = new
            {
                ExportTime = DateTime.Now,
                DataCount = _memoryCache.Count,
                Data = _memoryCache
            };

            var json = JsonConvert.SerializeObject(jsonData, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
        }

        /// <summary>
        /// 保存数据到CSV文件
        /// </summary>
        public async Task SaveToCsvAsync(string? fileName = null)
        {
            fileName ??= $"heartrate_data_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = Path.Combine(_dataDirectory, fileName);

            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, new CsvConfiguration(CultureInfo.InvariantCulture));
            
            await csv.WriteRecordsAsync(_memoryCache);
            await File.WriteAllTextAsync(filePath, writer.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// 保存数据到AI音乐创作专用格式
        /// </summary>
        public async Task SaveForMusicCreationAsync(string? fileName = null)
        {
            fileName ??= $"music_creation_data_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(_dataDirectory, fileName);

            var musicData = new
            {
                ExportTime = DateTime.Now,
                SessionInfo = new
                {
                    Duration = _memoryCache.Count > 0 ? 
                        (_memoryCache.Last().Timestamp - _memoryCache.First().Timestamp).TotalMinutes : 0,
                    DataPoints = _memoryCache.Count,
                    AverageHeartRate = _memoryCache.Count > 0 ? _memoryCache.Average(x => x.HeartRate) : 0,
                    MinHeartRate = _memoryCache.Count > 0 ? _memoryCache.Min(x => x.HeartRate) : 0,
                    MaxHeartRate = _memoryCache.Count > 0 ? _memoryCache.Max(x => x.HeartRate) : 0
                },
                MusicParameters = new
                {
                    TempoProgression = _memoryCache.Select(x => new { 
                        Time = x.Timestamp, 
                        Tempo = x.MusicTempo,
                        Intensity = x.Intensity 
                    }).ToList(),
                    KeyProgression = _memoryCache.Select(x => new { 
                        Time = x.Timestamp, 
                        Key = x.MusicKey,
                        Emotion = x.EmotionalState 
                    }).ToList(),
                    RhythmPatterns = _memoryCache.GroupBy(x => x.RhythmPattern)
                        .Select(g => new { Pattern = g.Key, Count = g.Count() }).ToList(),
                    EmotionalJourney = _memoryCache.Select(x => new { 
                        Time = x.Timestamp, 
                        State = x.EmotionalState,
                        Category = x.BpmCategory 
                    }).ToList()
                },
                RawData = _memoryCache
            };

            var json = JsonConvert.SerializeObject(musicData, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
        }

        /// <summary>
        /// 从JSON文件加载数据
        /// </summary>
        public async Task<List<HeartRateData>> LoadFromJsonAsync(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            var json = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            
            try
            {
                // 尝试加载完整格式
                var fullData = JsonConvert.DeserializeObject<dynamic>(json);
                if (fullData?.Data != null)
                {
                    var dataArray = JsonConvert.DeserializeObject<List<HeartRateData>>(fullData.Data.ToString());
                    return dataArray ?? new List<HeartRateData>();
                }
            }
            catch
            {
                // 尝试直接加载数据数组
                var dataArray = JsonConvert.DeserializeObject<List<HeartRateData>>(json);
                return dataArray ?? new List<HeartRateData>();
            }

            return new List<HeartRateData>();
        }

        /// <summary>
        /// 从CSV文件加载数据
        /// </summary>
        public async Task<List<HeartRateData>> LoadFromCsvAsync(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            using var reader = new StringReader(await File.ReadAllTextAsync(filePath, Encoding.UTF8));
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture));
            
            var records = csv.GetRecords<HeartRateData>().ToList();
            return records;
        }

        /// <summary>
        /// 获取数据目录中的所有文件
        /// </summary>
        public List<FileInfo> GetDataFiles(string pattern = "*.*")
        {
            var directory = new DirectoryInfo(_dataDirectory);
            return directory.GetFiles(pattern).OrderByDescending(f => f.CreationTime).ToList();
        }

        /// <summary>
        /// 删除指定文件
        /// </summary>
        public void DeleteFile(string fileName)
        {
            var filePath = Path.Combine(_dataDirectory, fileName);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        public DataStatistics GetStatistics()
        {
            if (_memoryCache.Count == 0)
                return new DataStatistics();

            var heartRates = _memoryCache.Select(x => x.HeartRate).ToList();
            var intensities = _memoryCache.Select(x => x.Intensity).ToList();

            return new DataStatistics
            {
                TotalDataPoints = _memoryCache.Count,
                Duration = (_memoryCache.Last().Timestamp - _memoryCache.First().Timestamp).TotalMinutes,
                AverageHeartRate = heartRates.Average(),
                MinHeartRate = heartRates.Min(),
                MaxHeartRate = heartRates.Max(),
                AverageIntensity = intensities.Average(),
                MostCommonBpmCategory = _memoryCache.GroupBy(x => x.BpmCategory)
                    .OrderByDescending(g => g.Count()).First().Key,
                MostCommonEmotionalState = _memoryCache.GroupBy(x => x.EmotionalState)
                    .OrderByDescending(g => g.Count()).First().Key,
                TempoRange = new { 
                    Min = _memoryCache.Min(x => x.MusicTempo), 
                    Max = _memoryCache.Max(x => x.MusicTempo) 
                }
            };
        }

        /// <summary>
        /// 获取数据目录路径
        /// </summary>
        public string GetDataDirectory() => _dataDirectory;
    }

    /// <summary>
    /// 数据统计信息
    /// </summary>
    public class DataStatistics
    {
        public int TotalDataPoints { get; set; }
        public double Duration { get; set; } // 分钟
        public double AverageHeartRate { get; set; }
        public int MinHeartRate { get; set; }
        public int MaxHeartRate { get; set; }
        public double AverageIntensity { get; set; }
        public string MostCommonBpmCategory { get; set; } = string.Empty;
        public string MostCommonEmotionalState { get; set; } = string.Empty;
        public object? TempoRange { get; set; }
    }
}
