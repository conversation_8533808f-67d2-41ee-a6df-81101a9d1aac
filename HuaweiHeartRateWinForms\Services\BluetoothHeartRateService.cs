using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using HuaweiHeartRateWinForms.Models;

namespace HuaweiHeartRateWinForms.Services
{
    /// <summary>
    /// 蓝牙心率服务
    /// </summary>
    public class BluetoothHeartRateService : IDisposable
    {
        private readonly System.Threading.Timer? _simulationTimer;
        private readonly Random _random = new();
        private bool _isConnected = false;
        private bool _isScanning = false;
        private bool _isMonitoring = false;
        private int _baseHeartRate = 70;
        private int _trend = 0; // -1下降，0稳定，1上升
        private int _trendCounter = 0;

        // 事件
        public event EventHandler<HeartRateData>? HeartRateReceived;
        public event EventHandler<BluetoothDevice>? DeviceFound;
        public event EventHandler<string>? ConnectionStatusChanged;
        public event EventHandler<string>? ErrorOccurred;

        // 属性
        public bool IsConnected => _isConnected;
        public bool IsScanning => _isScanning;
        public bool IsMonitoring => _isMonitoring;
        public string? ConnectedDeviceAddress { get; private set; }
        public string? ConnectedDeviceName { get; private set; }

        public BluetoothHeartRateService()
        {
            // 初始化模拟定时器
            _simulationTimer = new System.Threading.Timer(SimulateHeartRateData, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// 开始扫描蓝牙设备
        /// </summary>
        public async Task StartScanAsync(int timeoutSeconds = 10)
        {
            if (_isScanning) return;

            _isScanning = true;
            ConnectionStatusChanged?.Invoke(this, "开始扫描蓝牙设备...");

            try
            {
                // 模拟扫描过程
                await Task.Delay(1000);

                // 模拟发现设备
                var mockDevices = new List<BluetoothDevice>
                {
                    new("HUAWEI WATCH GT 3", "AA:BB:CC:DD:EE:FF", -45),
                    new("HUAWEI Band 7", "FF:EE:DD:CC:BB:AA", -52),
                    new("HUAWEI WATCH GT 2", "11:22:33:44:55:66", -38),
                    new("HUAWEI Band 6", "66:55:44:33:22:11", -60)
                };

                foreach (var device in mockDevices)
                {
                    await Task.Delay(500); // 模拟发现间隔
                    DeviceFound?.Invoke(this, device);
                }

                ConnectionStatusChanged?.Invoke(this, $"扫描完成，发现 {mockDevices.Count} 个设备");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"扫描失败: {ex.Message}");
            }
            finally
            {
                _isScanning = false;
            }
        }

        /// <summary>
        /// 停止扫描
        /// </summary>
        public void StopScan()
        {
            if (!_isScanning) return;

            _isScanning = false;
            ConnectionStatusChanged?.Invoke(this, "扫描已停止");
        }

        /// <summary>
        /// 连接到指定设备
        /// </summary>
        public async Task<bool> ConnectAsync(string deviceAddress, string deviceName)
        {
            if (_isConnected) return true;

            try
            {
                ConnectionStatusChanged?.Invoke(this, $"正在连接到 {deviceName}...");

                // 模拟连接过程
                await Task.Delay(2000);

                _isConnected = true;
                ConnectedDeviceAddress = deviceAddress;
                ConnectedDeviceName = deviceName;

                ConnectionStatusChanged?.Invoke(this, $"已连接到 {deviceName}");
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task DisconnectAsync()
        {
            if (!_isConnected) return;

            try
            {
                StopMonitoring();
                
                ConnectionStatusChanged?.Invoke(this, "正在断开连接...");
                await Task.Delay(1000);

                _isConnected = false;
                ConnectedDeviceAddress = null;
                ConnectedDeviceName = null;

                ConnectionStatusChanged?.Invoke(this, "已断开连接");
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"断开连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始心率监测
        /// </summary>
        public void StartMonitoring()
        {
            if (!_isConnected || _isMonitoring) return;

            _isMonitoring = true;
            ConnectionStatusChanged?.Invoke(this, "开始心率监测...");

            // 启动模拟数据生成
            _simulationTimer?.Change(0, 1000); // 每秒生成一次数据
        }

        /// <summary>
        /// 停止心率监测
        /// </summary>
        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            _isMonitoring = false;
            _simulationTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            
            ConnectionStatusChanged?.Invoke(this, "心率监测已停止");
        }

        /// <summary>
        /// 模拟心率数据生成
        /// </summary>
        private void SimulateHeartRateData(object? state)
        {
            if (!_isMonitoring || !_isConnected) return;

            try
            {
                // 模拟心率变化逻辑
                if (_trendCounter <= 0)
                {
                    _trend = _random.Next(-1, 2); // -1, 0, 1
                    _trendCounter = _random.Next(5, 16); // 5-15次测量保持同一趋势
                }

                // 生成心率变化
                var variation = (_random.NextDouble() - 0.5) * 10; // ±5的随机变化
                var trendEffect = _trend * _random.NextDouble() * 3; // 趋势影响

                _baseHeartRate += (int)(trendEffect + variation);
                _baseHeartRate = Math.Max(50, Math.Min(180, _baseHeartRate)); // 限制在合理范围内

                _trendCounter--;

                // 创建心率数据
                var heartRateData = new HeartRateData(
                    _baseHeartRate,
                    DateTime.Now,
                    ConnectedDeviceAddress,
                    ConnectedDeviceName
                );

                // 触发事件
                HeartRateReceived?.Invoke(this, heartRateData);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"生成心率数据失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _simulationTimer?.Dispose();
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 蓝牙设备信息
    /// </summary>
    public class BluetoothDevice
    {
        public string Name { get; }
        public string Address { get; }
        public int Rssi { get; }

        public BluetoothDevice(string name, string address, int rssi)
        {
            Name = name;
            Address = address;
            Rssi = rssi;
        }

        public override string ToString()
        {
            return $"{Name} ({Address}) - 信号强度: {Rssi} dBm";
        }
    }
}
